const gplay = require('google-play-scraper');
const fs = require('fs-extra');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const path = require('path');

class VoodooScraper {
    constructor() {
        this.developer = 'VOODOO';
        this.outputDir = './output';
        this.apps = [];
        this.detailedApps = [];
    }

    async init() {
        // 创建输出目录
        await fs.ensureDir(this.outputDir);
        console.log(`🚀 开始获取 ${this.developer} 的所有应用...`);
    }

    async getVoodooApps() {
        try {
            console.log('📱 搜索 VOODOO 开发的应用...');

            // 方法1: 通过开发者ID直接获取应用列表
            let allApps = [];

            try {
                console.log('🔍 尝试通过开发者ID获取应用...');
                const developerApps = await gplay.developer({
                    devId: 'VOODOO'
                });
                console.log(`📱 通过开发者ID找到 ${developerApps.length} 个应用`);
                allApps = allApps.concat(developerApps);
            } catch (devError) {
                console.log('⚠️ 开发者ID方法失败，尝试其他方法...');
            }

            // 方法2: 多关键词搜索
            const searchTerms = [
                'VOODOO',
                'voodoo games',
                'publisher:VOODOO',
                'developer:VOODOO'
            ];

            for (const term of searchTerms) {
                try {
                    console.log(`🔍 搜索关键词: "${term}"`);
                    const searchResults = await gplay.search({
                        term: term,
                        num: 250
                    });

                    // 过滤出 VOODOO 开发的应用
                    const voodooApps = searchResults.filter(app =>
                        app.developer &&
                        (app.developer.toUpperCase().includes('VOODOO') ||
                         app.developer.toLowerCase().includes('voodoo'))
                    );

                    console.log(`📱 关键词 "${term}" 找到 ${voodooApps.length} 个 VOODOO 应用`);

                    // 合并结果，避免重复
                    voodooApps.forEach(app => {
                        if (!allApps.find(existing => existing.appId === app.appId)) {
                            allApps.push(app);
                        }
                    });

                    // 添加延迟避免被限制
                    await this.sleep(1000);
                } catch (searchError) {
                    console.log(`⚠️ 搜索 "${term}" 失败:`, searchError.message);
                }
            }

            // 方法3: 通过已知热门应用的相关推荐获取更多应用
            const knownVoodooApps = [
                'io.voodoo.holeio',
                'com.vincentb.MobControl',
                'io.voodoo.paper2',
                'com.h8games.helixjump'
            ];

            for (const appId of knownVoodooApps) {
                try {
                    console.log(`🔍 获取 ${appId} 的相关应用...`);
                    const similar = await gplay.similar({
                        appId: appId,
                        num: 50
                    });

                    const voodooSimilar = similar.filter(app =>
                        app.developer &&
                        (app.developer.toUpperCase().includes('VOODOO') ||
                         app.developer.toLowerCase().includes('voodoo'))
                    );

                    console.log(`📱 从 ${appId} 相关应用中找到 ${voodooSimilar.length} 个 VOODOO 应用`);

                    voodooSimilar.forEach(app => {
                        if (!allApps.find(existing => existing.appId === app.appId)) {
                            allApps.push(app);
                        }
                    });

                    await this.sleep(1000);
                } catch (similarError) {
                    console.log(`⚠️ 获取 ${appId} 相关应用失败:`, similarError.message);
                }
            }

            this.apps = allApps;
            console.log(`✅ 总共找到 ${this.apps.length} 个 VOODOO 应用`);

            // 保存基础信息
            await this.saveBasicInfo();

            return this.apps;
        } catch (error) {
            console.error('❌ 搜索应用时出错:', error.message);
            throw error;
        }
    }

    async getDetailedInfo() {
        console.log('📊 获取详细信息...');
        
        for (let i = 0; i < this.apps.length; i++) {
            const app = this.apps[i];
            try {
                console.log(`📋 处理 ${i + 1}/${this.apps.length}: ${app.title}`);
                
                const details = await gplay.app({ appId: app.appId });
                
                // 获取评论
                let reviews = [];
                try {
                    reviews = await gplay.reviews({
                        appId: app.appId,
                        sort: gplay.sort.NEWEST,
                        num: 10
                    });
                } catch (reviewError) {
                    console.log(`⚠️  无法获取 ${app.title} 的评论: ${reviewError.message}`);
                }

                const detailedApp = {
                    // 基本信息
                    title: details.title,
                    appId: details.appId,
                    url: details.url,
                    icon: details.icon,
                    developer: details.developer,
                    developerId: details.developerId,
                    
                    // 分类和标签
                    genre: details.genre,
                    genreId: details.genreId,
                    familyGenre: details.familyGenre,
                    familyGenreId: details.familyGenreId,
                    
                    // 评分和统计
                    score: details.score,
                    scoreText: details.scoreText,
                    ratings: details.ratings,
                    reviews: details.reviews,
                    histogram: details.histogram,
                    
                    // 版本信息
                    version: details.version,
                    updated: details.updated,
                    recentChanges: details.recentChanges,
                    
                    // 应用详情
                    description: details.description,
                    descriptionHTML: details.descriptionHTML,
                    summary: details.summary,
                    installs: details.installs,
                    minInstalls: details.minInstalls,
                    maxInstalls: details.maxInstalls,
                    free: details.free,
                    price: details.price,
                    currency: details.currency,
                    priceText: details.priceText,
                    
                    // 技术信息
                    size: details.size,
                    androidVersion: details.androidVersion,
                    androidVersionText: details.androidVersionText,
                    contentRating: details.contentRating,
                    contentRatingDescription: details.contentRatingDescription,
                    adSupported: details.adSupported,
                    inAppProductPrice: details.inAppProductPrice,
                    containsAds: details.containsAds,
                    
                    // 媒体
                    screenshots: details.screenshots,
                    video: details.video,
                    videoImage: details.videoImage,
                    headerImage: details.headerImage,
                    
                    // 评论摘要
                    recentReviews: reviews.data ? reviews.data.slice(0, 5).map(review => ({
                        userName: review.userName,
                        userImage: review.userImage,
                        date: review.date,
                        score: review.score,
                        scoreText: review.scoreText,
                        text: review.text
                    })) : [],
                    
                    // 获取时间
                    scrapedAt: new Date().toISOString()
                };
                
                this.detailedApps.push(detailedApp);
                
                // 每处理5个应用暂停一下，避免被限制
                if ((i + 1) % 5 === 0) {
                    console.log('⏳ 暂停2秒...');
                    await this.sleep(2000);
                }
                
            } catch (error) {
                console.error(`❌ 获取 ${app.title} 详细信息时出错:`, error.message);
                // 继续处理下一个应用
                continue;
            }
        }
        
        console.log(`✅ 成功获取 ${this.detailedApps.length} 个应用的详细信息`);
    }

    async saveBasicInfo() {
        const basicInfoPath = path.join(this.outputDir, 'voodoo_apps_basic.json');
        await fs.writeJson(basicInfoPath, this.apps, { spaces: 2 });
        console.log(`💾 基础信息已保存到: ${basicInfoPath}`);
    }

    async saveDetailedInfo() {
        // 保存为 JSON
        const jsonPath = path.join(this.outputDir, 'voodoo_apps_detailed.json');
        await fs.writeJson(jsonPath, this.detailedApps, { spaces: 2 });
        console.log(`💾 详细信息已保存到: ${jsonPath}`);
        
        // 保存为 CSV
        const csvPath = path.join(this.outputDir, 'voodoo_apps_detailed.csv');
        const csvWriter = createCsvWriter({
            path: csvPath,
            header: [
                { id: 'title', title: '应用名称' },
                { id: 'appId', title: '应用ID' },
                { id: 'developer', title: '开发者' },
                { id: 'genre', title: '分类' },
                { id: 'score', title: '评分' },
                { id: 'ratings', title: '评分数量' },
                { id: 'installs', title: '安装次数' },
                { id: 'free', title: '免费' },
                { id: 'price', title: '价格' },
                { id: 'size', title: '大小' },
                { id: 'version', title: '版本' },
                { id: 'updated', title: '更新时间' },
                { id: 'androidVersion', title: '安卓版本要求' },
                { id: 'contentRating', title: '内容评级' },
                { id: 'containsAds', title: '包含广告' },
                { id: 'url', title: 'Play Store链接' },
                { id: 'scrapedAt', title: '获取时间' }
            ]
        });
        
        await csvWriter.writeRecords(this.detailedApps);
        console.log(`💾 CSV文件已保存到: ${csvPath}`);
    }

    async generateReport() {
        const reportPath = path.join(this.outputDir, 'voodoo_report.md');
        
        let report = `# VOODOO 应用分析报告

## 概览
- **开发者**: ${this.developer}
- **应用总数**: ${this.detailedApps.length}
- **报告生成时间**: ${new Date().toLocaleString('zh-CN')}

## 统计信息

### 分类分布
`;
        
        // 统计分类
        const genreStats = {};
        this.detailedApps.forEach(app => {
            genreStats[app.genre] = (genreStats[app.genre] || 0) + 1;
        });
        
        Object.entries(genreStats).forEach(([genre, count]) => {
            report += `- **${genre}**: ${count} 个应用\n`;
        });
        
        // 评分统计
        const scores = this.detailedApps.filter(app => app.score).map(app => app.score);
        if (scores.length > 0) {
            const avgScore = (scores.reduce((a, b) => a + b, 0) / scores.length).toFixed(2);
            report += `\n### 评分统计
- **平均评分**: ${avgScore}
- **最高评分**: ${Math.max(...scores)}
- **最低评分**: ${Math.min(...scores)}
`;
        }
        
        // 热门应用（按下载量）
        report += `\n### 热门应用（按下载量排序）
`;
        
        const sortedApps = this.detailedApps
            .filter(app => app.minInstalls)
            .sort((a, b) => b.minInstalls - a.minInstalls)
            .slice(0, 10);
            
        sortedApps.forEach((app, index) => {
            report += `${index + 1}. **${app.title}** - ${app.installs} 次下载, 评分: ${app.score || 'N/A'}\n`;
        });
        
        // 最新应用
        report += `\n### 最近更新的应用
`;
        
        const recentApps = this.detailedApps
            .filter(app => app.updated)
            .sort((a, b) => new Date(b.updated) - new Date(a.updated))
            .slice(0, 5);
            
        recentApps.forEach((app, index) => {
            report += `${index + 1}. **${app.title}** - 更新时间: ${app.updated}\n`;
        });
        
        await fs.writeFile(reportPath, report);
        console.log(`📊 分析报告已保存到: ${reportPath}`);
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async run() {
        try {
            await this.init();
            await this.getVoodooApps();
            await this.getDetailedInfo();
            await this.saveDetailedInfo();
            await this.generateReport();
            
            console.log('\n🎉 任务完成！');
            console.log(`📁 所有文件已保存到: ${path.resolve(this.outputDir)}`);
            console.log('\n文件列表:');
            console.log('- voodoo_apps_basic.json (基础信息)');
            console.log('- voodoo_apps_detailed.json (详细信息JSON)');
            console.log('- voodoo_apps_detailed.csv (详细信息CSV)');
            console.log('- voodoo_report.md (分析报告)');
            
        } catch (error) {
            console.error('❌ 程序执行出错:', error.message);
            process.exit(1);
        }
    }
}

// 运行程序
const scraper = new VoodooScraper();
scraper.run();

module.exports = VoodooScraper;
